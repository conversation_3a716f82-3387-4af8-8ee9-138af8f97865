# 图书管理系统

一个基于Spring Boot + Vue 3的现代化图书管理系统。

## 项目概述

### 核心功能模块
- **用户管理** - 读者注册、登录、个人信息管理
- **图书管理** - 图书增删改查、分类管理、库存管理
- **借阅管理** - 借书、还书、续借、预约
- **系统管理** - 管理员功能、统计报表、系统配置

## 技术架构

### 后端技术栈
- **框架**: Spring Boot 3.x
- **数据库**: MySQL 8.0
- **ORM**: MyBatis Plus
- **安全**: Spring Security + JWT
- **缓存**: Redis
- **文档**: Swagger/OpenAPI

### 前端技术栈
- **框架**: Vue 3 + TypeScript
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **构建工具**: Vite

## 项目结构

```
library-management-system/
├── backend/                 # 后端Spring Boot项目
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/
│   │   │   │   └── com/library/
│   │   │   │       ├── controller/     # 控制器层
│   │   │   │       ├── service/        # 服务层
│   │   │   │       ├── mapper/         # 数据访问层
│   │   │   │       ├── entity/         # 实体类
│   │   │   │       ├── dto/            # 数据传输对象
│   │   │   │       ├── vo/             # 视图对象
│   │   │   │       ├── config/         # 配置类
│   │   │   │       └── common/         # 公共组件
│   │   │   └── resources/
│   │   │       ├── application.yml     # 应用配置
│   │   │       └── mapper/             # MyBatis映射文件
│   │   └── test/                       # 测试代码
│   └── pom.xml                         # Maven配置
├── frontend/                # 前端Vue项目
│   ├── src/
│   │   ├── components/      # 公共组件
│   │   ├── views/          # 页面组件
│   │   ├── api/            # API接口
│   │   ├── stores/         # Pinia状态管理
│   │   ├── router/         # 路由配置
│   │   ├── types/          # TypeScript类型定义
│   │   └── utils/          # 工具函数
│   ├── package.json        # 依赖配置
│   └── vite.config.ts      # Vite配置
├── database/               # 数据库相关
│   ├── schema.sql          # 数据库表结构
│   └── data.sql           # 初始化数据
└── docs/                  # 项目文档
    ├── api.md             # API文档
    └── deployment.md      # 部署文档
```

## 快速开始

### 环境要求
- JDK 17+
- Node.js 16+
- MySQL 8.0+
- Redis 6.0+

### 后端启动
```bash
cd backend
mvn spring-boot:run
```

### 前端启动
```bash
cd frontend
npm install
npm run dev
```

## 开发计划

### 第一阶段（1-2周）
- [x] 搭建项目基础架构
- [ ] 完成数据库设计和初始化
- [ ] 实现用户认证和权限管理
- [ ] 完成基础的CRUD接口

### 第二阶段（2-3周）
- [ ] 实现图书管理功能
- [ ] 完成借阅管理核心逻辑
- [ ] 开发前端主要页面
- [ ] 集成前后端接口

### 第三阶段（1-2周）
- [ ] 实现高级功能（搜索、统计等）
- [ ] 完善用户体验
- [ ] 性能优化和测试
- [ ] 部署和上线

## 贡献指南

请参考 [CONTRIBUTING.md](CONTRIBUTING.md) 了解如何参与项目开发。

## 许可证

本项目采用 MIT 许可证，详情请参考 [LICENSE](LICENSE) 文件。
