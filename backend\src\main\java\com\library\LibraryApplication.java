package com.library;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 图书管理系统主应用类
 * 
 * <AUTHOR> Management System
 * @version 1.0.0
 */
@SpringBootApplication
@EnableScheduling
public class LibraryApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(LibraryApplication.class, args);
        System.out.println("图书管理系统启动成功！");
        System.out.println("API文档地址: http://localhost:8080/swagger-ui.html");
    }
}
