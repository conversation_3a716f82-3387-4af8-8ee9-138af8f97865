package com.library.controller;

import com.library.common.PageResult;
import com.library.common.Result;
import com.library.dto.BookCreateDTO;
import com.library.dto.BookQueryDTO;
import com.library.dto.BookUpdateDTO;
import com.library.service.BookService;
import com.library.vo.BookVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 图书管理控制器
 * 
 * <AUTHOR> Management System
 */
@RestController
@RequestMapping("/books")
@RequiredArgsConstructor
@Tag(name = "图书管理", description = "图书的增删改查等管理功能")
public class BookController {
    
    private final BookService bookService;
    
    /**
     * 分页查询图书
     */
    @GetMapping
    @Operation(summary = "分页查询图书", description = "根据条件分页查询图书列表")
    public Result<PageResult<BookVO>> getBooks(@Valid BookQueryDTO queryDTO) {
        PageResult<BookVO> result = bookService.getBooks(queryDTO);
        return Result.success(result);
    }
    
    /**
     * 根据ID获取图书详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取图书详情", description = "根据图书ID获取详细信息")
    public Result<BookVO> getBookById(@Parameter(description = "图书ID") @PathVariable Long id) {
        BookVO book = bookService.getBookById(id);
        return Result.success(book);
    }
    
    /**
     * 添加图书
     */
    @PostMapping
    @Operation(summary = "添加图书", description = "添加新的图书到系统中")
    @PreAuthorize("hasRole('ADMIN') or hasRole('LIBRARIAN')")
    public Result<BookVO> addBook(@Valid @RequestBody BookCreateDTO createDTO) {
        BookVO book = bookService.addBook(createDTO);
        return Result.success("图书添加成功", book);
    }
    
    /**
     * 更新图书
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新图书", description = "更新指定图书的信息")
    @PreAuthorize("hasRole('ADMIN') or hasRole('LIBRARIAN')")
    public Result<BookVO> updateBook(@Parameter(description = "图书ID") @PathVariable Long id,
                                   @Valid @RequestBody BookUpdateDTO updateDTO) {
        BookVO book = bookService.updateBook(id, updateDTO);
        return Result.success("图书更新成功", book);
    }
    
    /**
     * 删除图书
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除图书", description = "从系统中删除指定图书")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<String> deleteBook(@Parameter(description = "图书ID") @PathVariable Long id) {
        bookService.deleteBook(id);
        return Result.success("图书删除成功");
    }
}
