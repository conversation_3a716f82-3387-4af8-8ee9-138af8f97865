package com.library.controller;

import com.library.common.PageResult;
import com.library.common.Result;
import com.library.dto.BorrowRequest;
import com.library.dto.BorrowingQueryDTO;
import com.library.service.BorrowingService;
import com.library.vo.BorrowingRecordVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 借阅管理控制器
 * 
 * <AUTHOR> Management System
 */
@RestController
@RequestMapping("/api/borrowing")
@RequiredArgsConstructor
@Tag(name = "借阅管理", description = "图书借阅、归还、续借等功能")
public class BorrowingController {
    
    private final BorrowingService borrowingService;
    
    /**
     * 借书
     */
    @PostMapping("/borrow")
    @Operation(summary = "借书", description = "用户借阅图书")
    @PreAuthorize("hasAnyRole('READER', 'LIBRARIAN', 'ADMIN')")
    public Result<BorrowingRecordVO> borrowBook(@Valid @RequestBody BorrowRequest borrowRequest) {
        Long userId = getCurrentUserId();
        BorrowingRecordVO record = borrowingService.borrowBook(userId, borrowRequest);
        return Result.success(record);
    }
    
    /**
     * 还书
     */
    @PostMapping("/return/{recordId}")
    @Operation(summary = "还书", description = "归还借阅的图书")
    @PreAuthorize("hasAnyRole('READER', 'LIBRARIAN', 'ADMIN')")
    public Result<BorrowingRecordVO> returnBook(@Parameter(description = "借阅记录ID") @PathVariable Long recordId) {
        BorrowingRecordVO record = borrowingService.returnBook(recordId);
        return Result.success(record);
    }
    
    /**
     * 续借
     */
    @PostMapping("/renew/{recordId}")
    @Operation(summary = "续借", description = "延长图书借阅期限")
    @PreAuthorize("hasAnyRole('READER', 'LIBRARIAN', 'ADMIN')")
    public Result<BorrowingRecordVO> renewBook(
            @Parameter(description = "借阅记录ID") @PathVariable Long recordId,
            @Parameter(description = "续借天数") @RequestParam(defaultValue = "30") Integer renewalDays) {
        BorrowingRecordVO record = borrowingService.renewBook(recordId, renewalDays);
        return Result.success(record);
    }
    
    /**
     * 分页查询借阅记录
     */
    @GetMapping("/records")
    @Operation(summary = "查询借阅记录", description = "分页查询借阅记录")
    @PreAuthorize("hasAnyRole('LIBRARIAN', 'ADMIN')")
    public Result<PageResult<BorrowingRecordVO>> getBorrowingRecords(BorrowingQueryDTO queryDTO) {
        PageResult<BorrowingRecordVO> result = borrowingService.getBorrowingRecords(queryDTO);
        return Result.success(result);
    }
    
    /**
     * 获取当前用户的借阅记录
     */
    @GetMapping("/my-records")
    @Operation(summary = "我的借阅记录", description = "获取当前用户的借阅记录")
    @PreAuthorize("hasAnyRole('READER', 'LIBRARIAN', 'ADMIN')")
    public Result<PageResult<BorrowingRecordVO>> getMyBorrowingRecords(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Long pageNum,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Long pageSize) {
        Long userId = getCurrentUserId();
        PageResult<BorrowingRecordVO> result = borrowingService.getUserCurrentBorrowings(userId, pageNum, pageSize);
        return Result.success(result);
    }
    
    /**
     * 获取当前用户正在借阅的图书
     */
    @GetMapping("/current")
    @Operation(summary = "当前借阅", description = "获取当前用户正在借阅的图书")
    @PreAuthorize("hasAnyRole('READER', 'LIBRARIAN', 'ADMIN')")
    public Result<PageResult<BorrowingRecordVO>> getCurrentBorrowings(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Long pageNum,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Long pageSize) {
        Long userId = getCurrentUserId();
        BorrowingQueryDTO queryDTO = new BorrowingQueryDTO();
        queryDTO.setUserId(userId);
        queryDTO.setStatus("BORROWED");
        queryDTO.setPageNum(pageNum);
        queryDTO.setPageSize(pageSize);
        
        PageResult<BorrowingRecordVO> result = borrowingService.getBorrowingRecords(queryDTO);
        return Result.success(result);
    }
    
    /**
     * 根据ID获取借阅记录详情
     */
    @GetMapping("/records/{id}")
    @Operation(summary = "借阅记录详情", description = "根据ID获取借阅记录详情")
    @PreAuthorize("hasAnyRole('READER', 'LIBRARIAN', 'ADMIN')")
    public Result<BorrowingRecordVO> getBorrowingRecord(@Parameter(description = "借阅记录ID") @PathVariable Long id) {
        BorrowingRecordVO record = borrowingService.getBorrowingRecordById(id);
        return Result.success(record);
    }
    
    /**
     * 管理员强制归还图书
     */
    @PostMapping("/admin/force-return/{recordId}")
    @Operation(summary = "强制归还", description = "管理员强制归还图书")
    @PreAuthorize("hasAnyRole('LIBRARIAN', 'ADMIN')")
    public Result<BorrowingRecordVO> forceReturnBook(@Parameter(description = "借阅记录ID") @PathVariable Long recordId) {
        BorrowingRecordVO record = borrowingService.returnBook(recordId);
        return Result.success(record);
    }
    
    /**
     * 获取当前登录用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof org.springframework.security.core.userdetails.User) {
            org.springframework.security.core.userdetails.User userDetails = 
                (org.springframework.security.core.userdetails.User) authentication.getPrincipal();
            // 这里需要根据用户名查询用户ID，暂时返回1L作为示例
            // 在实际项目中，应该从UserDetailsService中获取完整的用户信息
            return 1L; // TODO: 实现根据用户名获取用户ID的逻辑
        }
        throw new RuntimeException("用户未登录");
    }
}
