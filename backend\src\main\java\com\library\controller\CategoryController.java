package com.library.controller;

import com.library.common.Result;
import com.library.dto.CategoryCreateDTO;
import com.library.service.CategoryService;
import com.library.vo.CategoryVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.util.List;

/**
 * 分类管理控制器
 * 
 * <AUTHOR> Management System
 */
@RestController
@RequestMapping("/categories")
@RequiredArgsConstructor
@Tag(name = "分类管理", description = "图书分类的管理功能")
public class CategoryController {
    
    private final CategoryService categoryService;
    
    /**
     * 获取分类树
     */
    @GetMapping("/tree")
    @Operation(summary = "获取分类树", description = "获取完整的分类树形结构")
    public Result<List<CategoryVO>> getCategoryTree() {
        List<CategoryVO> tree = categoryService.getCategoryTree();
        return Result.success(tree);
    }
    
    /**
     * 获取顶级分类
     */
    @GetMapping("/top")
    @Operation(summary = "获取顶级分类", description = "获取所有顶级分类列表")
    public Result<List<CategoryVO>> getTopCategories() {
        List<CategoryVO> categories = categoryService.getTopCategories();
        return Result.success(categories);
    }
    
    /**
     * 根据父分类ID获取子分类
     */
    @GetMapping("/children/{parentId}")
    @Operation(summary = "获取子分类", description = "根据父分类ID获取子分类列表")
    public Result<List<CategoryVO>> getCategoriesByParentId(
            @Parameter(description = "父分类ID") @PathVariable Long parentId) {
        List<CategoryVO> categories = categoryService.getCategoriesByParentId(parentId);
        return Result.success(categories);
    }
    
    /**
     * 根据ID获取分类详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取分类详情", description = "根据分类ID获取详细信息")
    public Result<CategoryVO> getCategoryById(@Parameter(description = "分类ID") @PathVariable Long id) {
        CategoryVO category = categoryService.getCategoryById(id);
        return Result.success(category);
    }
    
    /**
     * 添加分类
     */
    @PostMapping
    @Operation(summary = "添加分类", description = "添加新的图书分类")
    @PreAuthorize("hasRole('ADMIN') or hasRole('LIBRARIAN')")
    public Result<CategoryVO> addCategory(@Valid @RequestBody CategoryCreateDTO createDTO) {
        CategoryVO category = categoryService.addCategory(createDTO);
        return Result.success("分类添加成功", category);
    }
    
    /**
     * 更新分类
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新分类", description = "更新指定分类的信息")
    @PreAuthorize("hasRole('ADMIN') or hasRole('LIBRARIAN')")
    public Result<CategoryVO> updateCategory(@Parameter(description = "分类ID") @PathVariable Long id,
                                           @Valid @RequestBody CategoryCreateDTO updateDTO) {
        CategoryVO category = categoryService.updateCategory(id, updateDTO);
        return Result.success("分类更新成功", category);
    }
    
    /**
     * 删除分类
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除分类", description = "删除指定的图书分类")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<String> deleteCategory(@Parameter(description = "分类ID") @PathVariable Long id) {
        categoryService.deleteCategory(id);
        return Result.success("分类删除成功");
    }
}
