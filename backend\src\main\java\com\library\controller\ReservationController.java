package com.library.controller;

import com.library.common.PageResult;
import com.library.common.Result;
import com.library.dto.ReservationRequest;
import com.library.service.ReservationService;
import com.library.vo.ReservationVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 预约管理控制器
 * 
 * <AUTHOR> Management System
 */
@RestController
@RequestMapping("/api/reservations")
@RequiredArgsConstructor
@Tag(name = "预约管理", description = "图书预约功能")
public class ReservationController {
    
    private final ReservationService reservationService;
    
    /**
     * 创建预约
     */
    @PostMapping
    @Operation(summary = "创建预约", description = "用户预约图书")
    @PreAuthorize("hasAnyRole('READER', 'LIBRARIAN', 'ADMIN')")
    public Result<ReservationVO> createReservation(@Valid @RequestBody ReservationRequest reservationRequest) {
        Long userId = getCurrentUserId();
        ReservationVO reservation = reservationService.createReservation(userId, reservationRequest);
        return Result.success(reservation);
    }
    
    /**
     * 取消预约
     */
    @PostMapping("/cancel/{reservationId}")
    @Operation(summary = "取消预约", description = "取消图书预约")
    @PreAuthorize("hasAnyRole('READER', 'LIBRARIAN', 'ADMIN')")
    public Result<ReservationVO> cancelReservation(@Parameter(description = "预约ID") @PathVariable Long reservationId) {
        ReservationVO reservation = reservationService.cancelReservation(reservationId);
        return Result.success(reservation);
    }
    
    /**
     * 分页查询预约记录
     */
    @GetMapping
    @Operation(summary = "查询预约记录", description = "分页查询预约记录")
    @PreAuthorize("hasAnyRole('LIBRARIAN', 'ADMIN')")
    public Result<PageResult<ReservationVO>> getReservations(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Long pageNum,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Long pageSize,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "图书ID") @RequestParam(required = false) Long bookId,
            @Parameter(description = "状态") @RequestParam(required = false) String status) {
        PageResult<ReservationVO> result = reservationService.getReservations(pageNum, pageSize, userId, bookId, status);
        return Result.success(result);
    }
    
    /**
     * 获取当前用户的预约记录
     */
    @GetMapping("/my-reservations")
    @Operation(summary = "我的预约记录", description = "获取当前用户的预约记录")
    @PreAuthorize("hasAnyRole('READER', 'LIBRARIAN', 'ADMIN')")
    public Result<PageResult<ReservationVO>> getMyReservations(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Long pageNum,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Long pageSize) {
        Long userId = getCurrentUserId();
        PageResult<ReservationVO> result = reservationService.getUserActiveReservations(userId, pageNum, pageSize);
        return Result.success(result);
    }
    
    /**
     * 根据ID获取预约详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "预约详情", description = "根据ID获取预约详情")
    @PreAuthorize("hasAnyRole('READER', 'LIBRARIAN', 'ADMIN')")
    public Result<ReservationVO> getReservation(@Parameter(description = "预约ID") @PathVariable Long id) {
        ReservationVO reservation = reservationService.getReservationById(id);
        return Result.success(reservation);
    }
    
    /**
     * 管理员强制取消预约
     */
    @PostMapping("/admin/cancel/{reservationId}")
    @Operation(summary = "强制取消预约", description = "管理员强制取消预约")
    @PreAuthorize("hasAnyRole('LIBRARIAN', 'ADMIN')")
    public Result<ReservationVO> forceCancel(@Parameter(description = "预约ID") @PathVariable Long reservationId) {
        ReservationVO reservation = reservationService.cancelReservation(reservationId);
        return Result.success(reservation);
    }
    
    /**
     * 获取当前登录用户ID
     */
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof org.springframework.security.core.userdetails.User) {
            org.springframework.security.core.userdetails.User userDetails = 
                (org.springframework.security.core.userdetails.User) authentication.getPrincipal();
            // 这里需要根据用户名查询用户ID，暂时返回1L作为示例
            // 在实际项目中，应该从UserDetailsService中获取完整的用户信息
            return 1L; // TODO: 实现根据用户名获取用户ID的逻辑
        }
        throw new RuntimeException("用户未登录");
    }
}
