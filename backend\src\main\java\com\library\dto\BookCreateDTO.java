package com.library.dto;

import lombok.Data;

import javax.validation.constraints.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 图书创建DTO
 * 
 * <AUTHOR> Management System
 */
@Data
public class BookCreateDTO {
    
    /**
     * ISBN号
     */
    @Pattern(regexp = "^\\d{10}(\\d{3})?$", message = "ISBN格式不正确")
    private String isbn;
    
    /**
     * 书名
     */
    @NotBlank(message = "书名不能为空")
    @Size(max = 200, message = "书名长度不能超过200个字符")
    private String title;
    
    /**
     * 作者
     */
    @NotBlank(message = "作者不能为空")
    @Size(max = 100, message = "作者长度不能超过100个字符")
    private String author;
    
    /**
     * 出版社
     */
    @Size(max = 100, message = "出版社长度不能超过100个字符")
    private String publisher;
    
    /**
     * 出版日期
     */
    private LocalDate publishDate;
    
    /**
     * 分类ID
     */
    @NotNull(message = "分类不能为空")
    private Long categoryId;
    
    /**
     * 总册数
     */
    @NotNull(message = "总册数不能为空")
    @Min(value = 1, message = "总册数不能小于1")
    @Max(value = 9999, message = "总册数不能大于9999")
    private Integer totalCopies;
    
    /**
     * 价格
     */
    @DecimalMin(value = "0.01", message = "价格必须大于0")
    @DecimalMax(value = "9999.99", message = "价格不能超过9999.99")
    private BigDecimal price;
    
    /**
     * 图书描述
     */
    @Size(max = 1000, message = "图书描述长度不能超过1000个字符")
    private String description;
    
    /**
     * 封面图片URL
     */
    @Size(max = 255, message = "封面图片URL长度不能超过255个字符")
    private String coverImage;
    
    /**
     * 存放位置
     */
    @Size(max = 100, message = "存放位置长度不能超过100个字符")
    private String location;
}
