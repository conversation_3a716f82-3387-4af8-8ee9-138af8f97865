package com.library.dto;

import lombok.Data;

/**
 * 图书查询DTO
 * 
 * <AUTHOR> Management System
 */
@Data
public class BookQueryDTO {
    
    /**
     * 页码
     */
    private Long pageNum = 1L;
    
    /**
     * 每页大小
     */
    private Long pageSize = 10L;
    
    /**
     * 书名（模糊查询）
     */
    private String title;
    
    /**
     * 作者（模糊查询）
     */
    private String author;
    
    /**
     * 出版社（模糊查询）
     */
    private String publisher;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * ISBN号
     */
    private String isbn;
}
