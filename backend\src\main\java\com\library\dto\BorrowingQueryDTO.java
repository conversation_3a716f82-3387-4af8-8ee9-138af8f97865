package com.library.dto;

import lombok.Data;

/**
 * 借阅记录查询DTO
 * 
 * <AUTHOR> Management System
 */
@Data
public class BorrowingQueryDTO {
    
    /**
     * 页码
     */
    private Long pageNum = 1L;
    
    /**
     * 每页大小
     */
    private Long pageSize = 10L;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 图书ID
     */
    private Long bookId;
    
    /**
     * 借阅状态
     */
    private String status;
    
    /**
     * 是否逾期
     */
    private Boolean overdue;
}
