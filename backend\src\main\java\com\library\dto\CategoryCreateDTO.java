package com.library.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 分类创建DTO
 * 
 * <AUTHOR> Management System
 */
@Data
public class CategoryCreateDTO {
    
    /**
     * 分类名称
     */
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 100, message = "分类名称长度不能超过100个字符")
    private String name;
    
    /**
     * 分类描述
     */
    @Size(max = 500, message = "分类描述长度不能超过500个字符")
    private String description;
    
    /**
     * 父分类ID，0表示顶级分类
     */
    private Long parentId = 0L;
    
    /**
     * 排序序号
     */
    private Integer sortOrder = 0;
}
