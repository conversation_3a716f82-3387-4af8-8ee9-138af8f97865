package com.library.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 图书实体类
 * 
 * <AUTHOR> Management System
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("books")
public class Book extends BaseEntity {
    
    /**
     * ISBN号
     */
    private String isbn;
    
    /**
     * 书名
     */
    private String title;
    
    /**
     * 作者
     */
    private String author;
    
    /**
     * 出版社
     */
    private String publisher;
    
    /**
     * 出版日期
     */
    @TableField("publish_date")
    private LocalDate publishDate;
    
    /**
     * 分类ID
     */
    @TableField("category_id")
    private Long categoryId;
    
    /**
     * 总册数
     */
    @TableField("total_copies")
    private Integer totalCopies;
    
    /**
     * 可借册数
     */
    @TableField("available_copies")
    private Integer availableCopies;
    
    /**
     * 价格
     */
    private BigDecimal price;
    
    /**
     * 图书描述
     */
    private String description;
    
    /**
     * 封面图片URL
     */
    @TableField("cover_image")
    private String coverImage;
    
    /**
     * 存放位置
     */
    private String location;
    
    /**
     * 分类名称（非数据库字段，用于查询结果展示）
     */
    @TableField(exist = false)
    private String categoryName;
}
