package com.library.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 借阅记录实体类
 * 
 * <AUTHOR> Management System
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("borrowing_records")
public class BorrowingRecord extends BaseEntity {
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 图书ID
     */
    @TableField("book_id")
    private Long bookId;
    
    /**
     * 借阅时间
     */
    @TableField("borrow_time")
    private LocalDateTime borrowTime;

    /**
     * 应还时间
     */
    @TableField("due_time")
    private LocalDateTime dueTime;

    /**
     * 实际归还时间
     */
    @TableField("return_time")
    private LocalDateTime returnTime;

    /**
     * 续借次数
     */
    @TableField("renewal_count")
    private Integer renewalCount;
    
    /**
     * 状态 (BORROWED: 已借出, RETURNED: 已归还, OVERDUE: 逾期, LOST: 丢失)
     */
    private BorrowStatus status;
    
    /**
     * 罚金金额
     */
    @TableField("fine_amount")
    private BigDecimal fineAmount;
    
    /**
     * 备注
     */
    private String remarks;
    
    /**
     * 用户名（非数据库字段，用于查询结果展示）
     */
    @TableField(exist = false)
    private String username;
    
    /**
     * 图书标题（非数据库字段，用于查询结果展示）
     */
    @TableField(exist = false)
    private String bookTitle;
    
    /**
     * 图书作者（非数据库字段，用于查询结果展示）
     */
    @TableField(exist = false)
    private String bookAuthor;
    
    /**
     * 借阅状态枚举
     */
    public enum BorrowStatus {
        BORROWED, RETURNED, OVERDUE, LOST
    }
}
