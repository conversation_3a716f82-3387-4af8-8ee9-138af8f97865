package com.library.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 图书分类实体类
 * 
 * <AUTHOR> Management System
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("categories")
public class Category extends BaseEntity {
    
    /**
     * 分类名称
     */
    private String name;
    
    /**
     * 分类描述
     */
    private String description;
    
    /**
     * 父分类ID，0表示顶级分类
     */
    @TableField("parent_id")
    private Long parentId;
    
    /**
     * 排序序号
     */
    @TableField("sort_order")
    private Integer sortOrder;
}
