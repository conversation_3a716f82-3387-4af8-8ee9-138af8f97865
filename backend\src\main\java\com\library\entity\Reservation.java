package com.library.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 预约实体类
 * 
 * <AUTHOR> Management System
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("reservations")
public class Reservation extends BaseEntity {
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 图书ID
     */
    @TableField("book_id")
    private Long bookId;
    
    /**
     * 预约时间
     */
    @TableField("reservation_time")
    private LocalDateTime reservationTime;
    
    /**
     * 过期时间
     */
    @TableField("expiration_time")
    private LocalDateTime expirationTime;
    
    /**
     * 状态 (ACTIVE: 有效, CANCELLED: 已取消, EXPIRED: 已过期, FULFILLED: 已完成)
     */
    private ReservationStatus status;
    
    /**
     * 备注
     */
    private String remarks;
    
    /**
     * 用户名（非数据库字段，用于查询结果展示）
     */
    @TableField(exist = false)
    private String username;
    
    /**
     * 用户真实姓名（非数据库字段，用于查询结果展示）
     */
    @TableField(exist = false)
    private String userRealName;
    
    /**
     * 图书标题（非数据库字段，用于查询结果展示）
     */
    @TableField(exist = false)
    private String bookTitle;
    
    /**
     * 图书作者（非数据库字段，用于查询结果展示）
     */
    @TableField(exist = false)
    private String bookAuthor;
    
    /**
     * 图书ISBN（非数据库字段，用于查询结果展示）
     */
    @TableField(exist = false)
    private String bookIsbn;
    
    /**
     * 预约状态枚举
     */
    public enum ReservationStatus {
        ACTIVE, CANCELLED, EXPIRED, FULFILLED
    }
}
