package com.library.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户实体类
 * 
 * <AUTHOR> Management System
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("users")
public class User extends BaseEntity {
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 真实姓名
     */
    @TableField("real_name")
    private String realName;
    
    /**
     * 头像URL
     */
    private String avatar;
    
    /**
     * 角色 (ADMIN: 管理员, LIBRARIAN: 图书管理员, READER: 读者)
     */
    private UserRole role;
    
    /**
     * 状态 (ACTIVE: 激活, INACTIVE: 未激活, BANNED: 禁用)
     */
    private UserStatus status;
    
    /**
     * 最后登录时间
     */
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;
    
    /**
     * 用户角色枚举
     */
    public enum UserRole {
        ADMIN, LIBRARIAN, READER
    }
    
    /**
     * 用户状态枚举
     */
    public enum UserStatus {
        ACTIVE, INACTIVE, BANNED
    }
}
