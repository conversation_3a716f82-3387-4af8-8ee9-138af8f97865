package com.library.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.library.entity.Book;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 图书数据访问层
 * 
 * <AUTHOR> Management System
 */
@Mapper
public interface BookMapper extends BaseMapper<Book> {
    
    /**
     * 分页查询图书（包含分类名称）
     * 
     * @param page 分页参数
     * @param title 书名（模糊查询）
     * @param author 作者（模糊查询）
     * @param categoryId 分类ID
     * @return 图书分页结果
     */
    @Select("<script>" +
            "SELECT b.*, c.name as category_name " +
            "FROM books b " +
            "LEFT JOIN categories c ON b.category_id = c.id " +
            "WHERE b.deleted = 0 " +
            "<if test='title != null and title != \"\"'>" +
            "AND b.title LIKE CONCAT('%', #{title}, '%') " +
            "</if>" +
            "<if test='author != null and author != \"\"'>" +
            "AND b.author LIKE CONCAT('%', #{author}, '%') " +
            "</if>" +
            "<if test='categoryId != null'>" +
            "AND b.category_id = #{categoryId} " +
            "</if>" +
            "ORDER BY b.created_at DESC" +
            "</script>")
    IPage<Book> selectBooksWithCategory(Page<Book> page, 
                                       @Param("title") String title,
                                       @Param("author") String author, 
                                       @Param("categoryId") Long categoryId);
    
    /**
     * 根据ISBN查询图书
     * 
     * @param isbn ISBN号
     * @return 图书信息
     */
    @Select("SELECT * FROM books WHERE isbn = #{isbn} AND deleted = 0")
    Book findByIsbn(@Param("isbn") String isbn);
    
    /**
     * 更新图书可借数量
     * 
     * @param bookId 图书ID
     * @param change 变化数量（正数增加，负数减少）
     * @return 影响行数
     */
    @Update("UPDATE books SET available_copies = available_copies + #{change} WHERE id = #{bookId} AND deleted = 0")
    int updateAvailableCopies(@Param("bookId") Long bookId, @Param("change") Integer change);
}
