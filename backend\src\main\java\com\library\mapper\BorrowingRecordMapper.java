package com.library.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.library.entity.BorrowingRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 借阅记录数据访问层
 * 
 * <AUTHOR> Management System
 */
@Mapper
public interface BorrowingRecordMapper extends BaseMapper<BorrowingRecord> {
    
    /**
     * 分页查询借阅记录（包含用户和图书信息）
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @param bookId 图书ID
     * @param status 借阅状态
     * @param overdue 是否逾期
     * @return 借阅记录分页结果
     */
    @Select("<script>" +
            "SELECT br.*, u.username, u.real_name, b.title as book_title, b.author as book_author, b.isbn as book_isbn " +
            "FROM borrowing_records br " +
            "LEFT JOIN users u ON br.user_id = u.id " +
            "LEFT JOIN books b ON br.book_id = b.id " +
            "WHERE 1=1 " +
            "<if test='userId != null'>" +
            "AND br.user_id = #{userId} " +
            "</if>" +
            "<if test='bookId != null'>" +
            "AND br.book_id = #{bookId} " +
            "</if>" +
            "<if test='status != null'>" +
            "AND br.status = #{status} " +
            "</if>" +
            "<if test='overdue != null and overdue == true'>" +
            "AND br.status = 'BORROWED' AND br.due_time &lt; NOW() " +
            "</if>" +
            "<if test='overdue != null and overdue == false'>" +
            "AND (br.status != 'BORROWED' OR br.due_time &gt;= NOW()) " +
            "</if>" +
            "ORDER BY br.borrow_time DESC" +
            "</script>")
    IPage<BorrowingRecord> selectBorrowingRecordsWithDetails(Page<BorrowingRecord> page,
                                                           @Param("userId") Long userId,
                                                           @Param("bookId") Long bookId,
                                                           @Param("status") String status,
                                                           @Param("overdue") Boolean overdue);
    
    /**
     * 查询用户当前借阅的图书数量
     *
     * @param userId 用户ID
     * @return 借阅数量
     */
    @Select("SELECT COUNT(*) FROM borrowing_records WHERE user_id = #{userId} AND status = 'BORROWED'")
    int countActiveByUser(@Param("userId") Long userId);

    /**
     * 查询用户是否已借阅某本图书
     *
     * @param userId 用户ID
     * @param bookId 图书ID
     * @return 借阅记录
     */
    @Select("SELECT * FROM borrowing_records WHERE user_id = #{userId} AND book_id = #{bookId} AND status = 'BORROWED'")
    BorrowingRecord findActiveByUserAndBook(@Param("userId") Long userId, @Param("bookId") Long bookId);
    
    /**
     * 查询逾期的借阅记录
     *
     * @return 逾期记录列表
     */
    @Select("SELECT br.*, u.username, u.real_name, b.title as book_title, b.author as book_author, b.isbn as book_isbn " +
            "FROM borrowing_records br " +
            "LEFT JOIN users u ON br.user_id = u.id " +
            "LEFT JOIN books b ON br.book_id = b.id " +
            "WHERE br.status = 'BORROWED' AND br.due_time < NOW()")
    List<BorrowingRecord> findOverdueRecords();
}
