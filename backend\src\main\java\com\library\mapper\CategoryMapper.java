package com.library.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.library.entity.Category;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 图书分类数据访问层
 * 
 * <AUTHOR> Management System
 */
@Mapper
public interface CategoryMapper extends BaseMapper<Category> {
    
    /**
     * 根据父分类ID查询子分类列表
     * 
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    @Select("SELECT * FROM categories WHERE parent_id = #{parentId} AND deleted = 0 ORDER BY sort_order ASC")
    List<Category> findByParentId(@Param("parentId") Long parentId);
    
    /**
     * 查询所有顶级分类
     * 
     * @return 顶级分类列表
     */
    @Select("SELECT * FROM categories WHERE parent_id = 0 AND deleted = 0 ORDER BY sort_order ASC")
    List<Category> findTopCategories();
}
