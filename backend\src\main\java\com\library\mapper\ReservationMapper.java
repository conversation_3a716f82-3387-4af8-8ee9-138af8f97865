package com.library.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.library.entity.Reservation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 预约数据访问层
 * 
 * <AUTHOR> Management System
 */
@Mapper
public interface ReservationMapper extends BaseMapper<Reservation> {
    
    /**
     * 分页查询预约记录（包含用户和图书信息）
     * 
     * @param page 分页参数
     * @param userId 用户ID
     * @param bookId 图书ID
     * @param status 预约状态
     * @return 预约记录分页结果
     */
    @Select("<script>" +
            "SELECT r.*, u.username, u.real_name, b.title as book_title, b.author as book_author, b.isbn as book_isbn " +
            "FROM reservations r " +
            "LEFT JOIN users u ON r.user_id = u.id " +
            "LEFT JOIN books b ON r.book_id = b.id " +
            "WHERE 1=1 " +
            "<if test='userId != null'>" +
            "AND r.user_id = #{userId} " +
            "</if>" +
            "<if test='bookId != null'>" +
            "AND r.book_id = #{bookId} " +
            "</if>" +
            "<if test='status != null'>" +
            "AND r.status = #{status} " +
            "</if>" +
            "ORDER BY r.reservation_time DESC" +
            "</script>")
    IPage<Reservation> selectReservationsWithDetails(Page<Reservation> page,
                                                    @Param("userId") Long userId,
                                                    @Param("bookId") Long bookId,
                                                    @Param("status") String status);
    
    /**
     * 查询用户当前预约的图书数量
     * 
     * @param userId 用户ID
     * @return 预约数量
     */
    @Select("SELECT COUNT(*) FROM reservations WHERE user_id = #{userId} AND status = 'ACTIVE'")
    int countActiveByUser(@Param("userId") Long userId);
    
    /**
     * 查询用户是否已预约某本图书
     * 
     * @param userId 用户ID
     * @param bookId 图书ID
     * @return 预约记录
     */
    @Select("SELECT * FROM reservations WHERE user_id = #{userId} AND book_id = #{bookId} AND status = 'ACTIVE'")
    Reservation findActiveByUserAndBook(@Param("userId") Long userId, @Param("bookId") Long bookId);
    
    /**
     * 更新过期的预约记录
     * 
     * @return 更新行数
     */
    @Update("UPDATE reservations SET status = 'EXPIRED' WHERE status = 'ACTIVE' AND expiration_time < NOW()")
    int updateExpiredReservations();
}
