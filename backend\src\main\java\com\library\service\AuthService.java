package com.library.service;

import com.library.common.BusinessException;
import com.library.common.JwtUtils;
import com.library.dto.LoginRequest;
import com.library.dto.RegisterRequest;
import com.library.entity.User;
import com.library.mapper.UserMapper;
import com.library.vo.LoginResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 认证服务类
 * 
 * <AUTHOR> Management System
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuthService {
    
    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;
    private final AuthenticationManager authenticationManager;
    private final JwtUtils jwtUtils;
    
    /**
     * 用户登录
     * 
     * @param loginRequest 登录请求
     * @return 登录响应
     */
    public LoginResponse login(LoginRequest loginRequest) {
        try {
            // 认证用户
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                    loginRequest.getUsername(),
                    loginRequest.getPassword()
                )
            );
            
            // 获取用户信息
            User user = userMapper.findByUsername(loginRequest.getUsername());
            if (user == null) {
                throw new BusinessException("用户不存在");
            }
            
            // 生成JWT Token
            String token = jwtUtils.generateToken(
                user.getId(),
                user.getUsername(),
                user.getRole().name()
            );
            
            // 更新最后登录时间
            userMapper.updateLastLoginTime(user.getId(), LocalDateTime.now().toString());
            
            // 构建响应
            LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
            userInfo.setId(user.getId());
            userInfo.setUsername(user.getUsername());
            userInfo.setEmail(user.getEmail());
            userInfo.setRealName(user.getRealName());
            userInfo.setAvatar(user.getAvatar());
            userInfo.setRole(user.getRole().name());
            userInfo.setStatus(user.getStatus().name());
            
            log.info("User {} logged in successfully", user.getUsername());
            return new LoginResponse(token, userInfo);
            
        } catch (Exception e) {
            log.error("Login failed for user {}: {}", loginRequest.getUsername(), e.getMessage());
            throw new BusinessException("用户名或密码错误");
        }
    }
    
    /**
     * 用户注册
     * 
     * @param registerRequest 注册请求
     */
    @Transactional
    public void register(RegisterRequest registerRequest) {
        // 验证密码确认
        if (!registerRequest.getPassword().equals(registerRequest.getConfirmPassword())) {
            throw new BusinessException("两次输入的密码不一致");
        }
        
        // 检查用户名是否已存在
        if (userMapper.findByUsername(registerRequest.getUsername()) != null) {
            throw new BusinessException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (userMapper.findByEmail(registerRequest.getEmail()) != null) {
            throw new BusinessException("邮箱已被注册");
        }
        
        // 创建新用户
        User user = new User();
        user.setUsername(registerRequest.getUsername());
        user.setPassword(passwordEncoder.encode(registerRequest.getPassword()));
        user.setEmail(registerRequest.getEmail());
        user.setPhone(registerRequest.getPhone());
        user.setRealName(registerRequest.getRealName());
        user.setRole(User.UserRole.READER);
        user.setStatus(User.UserStatus.ACTIVE);
        
        userMapper.insert(user);
        
        log.info("User {} registered successfully", user.getUsername());
    }
}
