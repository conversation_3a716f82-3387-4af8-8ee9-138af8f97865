package com.library.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.library.common.BusinessException;
import com.library.common.PageResult;
import com.library.dto.BookCreateDTO;
import com.library.dto.BookQueryDTO;
import com.library.dto.BookUpdateDTO;
import com.library.entity.Book;
import com.library.entity.Category;
import com.library.mapper.BookMapper;
import com.library.mapper.CategoryMapper;
import com.library.vo.BookVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.stream.Collectors;

/**
 * 图书服务类
 * 
 * <AUTHOR> Management System
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BookService {
    
    private final BookMapper bookMapper;
    private final CategoryMapper categoryMapper;
    
    /**
     * 分页查询图书
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @Cacheable(value = "books", key = "#queryDTO.toString()")
    public PageResult<BookVO> getBooks(BookQueryDTO queryDTO) {
        Page<Book> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        
        // 使用自定义查询方法，包含分类名称
        Page<Book> bookPage = (Page<Book>) bookMapper.selectBooksWithCategory(
            page,
            queryDTO.getTitle(),
            queryDTO.getAuthor(),
            queryDTO.getCategoryId()
        );
        
        // 转换为VO
        PageResult<BookVO> result = new PageResult<>();
        result.setCurrent(bookPage.getCurrent());
        result.setSize(bookPage.getSize());
        result.setTotal(bookPage.getTotal());
        result.setPages(bookPage.getPages());
        result.setRecords(bookPage.getRecords().stream()
            .map(book -> BeanUtil.copyProperties(book, BookVO.class))
            .collect(Collectors.toList()));
        
        return result;
    }
    
    /**
     * 根据ID获取图书详情
     * 
     * @param id 图书ID
     * @return 图书详情
     */
    @Cacheable(value = "book", key = "#id")
    public BookVO getBookById(Long id) {
        Book book = bookMapper.selectById(id);
        if (book == null) {
            throw new BusinessException("图书不存在");
        }
        
        BookVO bookVO = BeanUtil.copyProperties(book, BookVO.class);
        
        // 获取分类名称
        if (book.getCategoryId() != null) {
            Category category = categoryMapper.selectById(book.getCategoryId());
            if (category != null) {
                bookVO.setCategoryName(category.getName());
            }
        }
        
        return bookVO;
    }
    
    /**
     * 添加图书
     * 
     * @param createDTO 创建DTO
     * @return 图书详情
     */
    @Transactional
    @CacheEvict(value = {"books", "book"}, allEntries = true)
    public BookVO addBook(BookCreateDTO createDTO) {
        // 检查ISBN是否已存在
        if (StringUtils.hasText(createDTO.getIsbn())) {
            Book existingBook = bookMapper.findByIsbn(createDTO.getIsbn());
            if (existingBook != null) {
                throw new BusinessException("ISBN号已存在");
            }
        }
        
        // 检查分类是否存在
        if (categoryMapper.selectById(createDTO.getCategoryId()) == null) {
            throw new BusinessException("分类不存在");
        }
        
        // 创建图书
        Book book = BeanUtil.copyProperties(createDTO, Book.class);
        book.setAvailableCopies(createDTO.getTotalCopies());
        
        bookMapper.insert(book);
        
        log.info("Book added: {}", book.getTitle());
        return getBookById(book.getId());
    }
    
    /**
     * 更新图书
     * 
     * @param id 图书ID
     * @param updateDTO 更新DTO
     * @return 图书详情
     */
    @Transactional
    @CacheEvict(value = {"books", "book"}, allEntries = true)
    public BookVO updateBook(Long id, BookUpdateDTO updateDTO) {
        Book existingBook = bookMapper.selectById(id);
        if (existingBook == null) {
            throw new BusinessException("图书不存在");
        }
        
        // 检查ISBN是否被其他图书使用
        if (StringUtils.hasText(updateDTO.getIsbn()) && 
            !updateDTO.getIsbn().equals(existingBook.getIsbn())) {
            Book bookWithSameIsbn = bookMapper.findByIsbn(updateDTO.getIsbn());
            if (bookWithSameIsbn != null && !bookWithSameIsbn.getId().equals(id)) {
                throw new BusinessException("ISBN号已被其他图书使用");
            }
        }
        
        // 检查分类是否存在
        if (categoryMapper.selectById(updateDTO.getCategoryId()) == null) {
            throw new BusinessException("分类不存在");
        }
        
        // 更新图书信息
        Book book = BeanUtil.copyProperties(updateDTO, Book.class);
        book.setId(id);
        
        // 如果总册数发生变化，需要调整可借册数
        if (!updateDTO.getTotalCopies().equals(existingBook.getTotalCopies())) {
            int borrowedCopies = existingBook.getTotalCopies() - existingBook.getAvailableCopies();
            int newAvailableCopies = updateDTO.getTotalCopies() - borrowedCopies;
            
            if (newAvailableCopies < 0) {
                throw new BusinessException("总册数不能小于已借出册数");
            }
            
            book.setAvailableCopies(newAvailableCopies);
        } else {
            book.setAvailableCopies(existingBook.getAvailableCopies());
        }
        
        bookMapper.updateById(book);
        
        log.info("Book updated: {}", book.getTitle());
        return getBookById(id);
    }
    
    /**
     * 删除图书
     * 
     * @param id 图书ID
     */
    @Transactional
    @CacheEvict(value = {"books", "book"}, allEntries = true)
    public void deleteBook(Long id) {
        Book book = bookMapper.selectById(id);
        if (book == null) {
            throw new BusinessException("图书不存在");
        }
        
        // 检查是否有未归还的借阅记录
        if (!book.getAvailableCopies().equals(book.getTotalCopies())) {
            throw new BusinessException("该图书还有未归还的借阅记录，无法删除");
        }
        
        bookMapper.deleteById(id);
        
        log.info("Book deleted: {}", book.getTitle());
    }
}
