package com.library.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.library.common.BusinessException;
import com.library.common.PageResult;
import com.library.dto.BorrowRequest;
import com.library.dto.BorrowingQueryDTO;
import com.library.entity.Book;
import com.library.entity.BorrowingRecord;
import com.library.entity.User;
import com.library.mapper.BookMapper;
import com.library.mapper.BorrowingRecordMapper;
import com.library.mapper.UserMapper;
import com.library.vo.BorrowingRecordVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.stream.Collectors;

/**
 * 借阅管理服务类
 * 
 * <AUTHOR> Management System
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BorrowingService {
    
    private final BorrowingRecordMapper borrowingRecordMapper;
    private final BookMapper bookMapper;
    private final UserMapper userMapper;
    
    /**
     * 借书
     * 
     * @param userId 用户ID
     * @param borrowRequest 借书请求
     * @return 借阅记录
     */
    @Transactional
    public BorrowingRecordVO borrowBook(Long userId, BorrowRequest borrowRequest) {
        // 检查用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 检查图书是否存在
        Book book = bookMapper.selectById(borrowRequest.getBookId());
        if (book == null) {
            throw new BusinessException("图书不存在");
        }
        
        // 检查图书是否可借
        if (book.getAvailableCopies() <= 0) {
            throw new BusinessException("图书库存不足，无法借阅");
        }
        
        // 检查用户是否已借阅该图书且未归还
        BorrowingRecord existingRecord = borrowingRecordMapper.findActiveByUserAndBook(userId, borrowRequest.getBookId());
        if (existingRecord != null) {
            throw new BusinessException("您已借阅该图书，请先归还后再借阅");
        }
        
        // 检查用户当前借阅数量是否超限（假设最多借5本）
        int currentBorrowCount = borrowingRecordMapper.countActiveByUser(userId);
        if (currentBorrowCount >= 5) {
            throw new BusinessException("您当前借阅的图书数量已达上限（5本），请先归还部分图书");
        }
        
        // 创建借阅记录
        BorrowingRecord borrowingRecord = new BorrowingRecord();
        borrowingRecord.setUserId(userId);
        borrowingRecord.setBookId(borrowRequest.getBookId());
        borrowingRecord.setBorrowTime(LocalDateTime.now());
        borrowingRecord.setDueTime(LocalDateTime.now().plusDays(borrowRequest.getBorrowDays()));
        borrowingRecord.setRenewalCount(0);
        borrowingRecord.setStatus(BorrowingRecord.BorrowStatus.BORROWED);
        
        borrowingRecordMapper.insert(borrowingRecord);
        
        // 更新图书可借数量
        bookMapper.updateAvailableCopies(borrowRequest.getBookId(), -1);
        
        log.info("User {} borrowed book {}", userId, borrowRequest.getBookId());
        
        return getBorrowingRecordById(borrowingRecord.getId());
    }
    
    /**
     * 还书
     * 
     * @param recordId 借阅记录ID
     * @return 借阅记录
     */
    @Transactional
    public BorrowingRecordVO returnBook(Long recordId) {
        BorrowingRecord record = borrowingRecordMapper.selectById(recordId);
        if (record == null) {
            throw new BusinessException("借阅记录不存在");
        }
        
        if (record.getStatus() != BorrowingRecord.BorrowStatus.BORROWED) {
            throw new BusinessException("该图书已归还或状态异常");
        }
        
        // 更新借阅记录
        record.setReturnTime(LocalDateTime.now());
        record.setStatus(BorrowingRecord.BorrowStatus.RETURNED);
        borrowingRecordMapper.updateById(record);
        
        // 更新图书可借数量
        bookMapper.updateAvailableCopies(record.getBookId(), 1);
        
        log.info("Book {} returned, record ID: {}", record.getBookId(), recordId);
        
        return getBorrowingRecordById(recordId);
    }
    
    /**
     * 续借
     * 
     * @param recordId 借阅记录ID
     * @param renewalDays 续借天数
     * @return 借阅记录
     */
    @Transactional
    public BorrowingRecordVO renewBook(Long recordId, Integer renewalDays) {
        BorrowingRecord record = borrowingRecordMapper.selectById(recordId);
        if (record == null) {
            throw new BusinessException("借阅记录不存在");
        }
        
        if (record.getStatus() != BorrowingRecord.BorrowStatus.BORROWED) {
            throw new BusinessException("该图书已归还或状态异常，无法续借");
        }
        
        // 检查续借次数限制（假设最多续借2次）
        if (record.getRenewalCount() >= 2) {
            throw new BusinessException("续借次数已达上限，无法继续续借");
        }
        
        // 更新借阅记录
        record.setDueTime(record.getDueTime().plusDays(renewalDays));
        record.setRenewalCount(record.getRenewalCount() + 1);
        borrowingRecordMapper.updateById(record);
        
        log.info("Book {} renewed, record ID: {}, renewal count: {}", 
                record.getBookId(), recordId, record.getRenewalCount());
        
        return getBorrowingRecordById(recordId);
    }
    
    /**
     * 分页查询借阅记录
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    public PageResult<BorrowingRecordVO> getBorrowingRecords(BorrowingQueryDTO queryDTO) {
        Page<BorrowingRecord> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        
        Page<BorrowingRecord> recordPage = (Page<BorrowingRecord>) borrowingRecordMapper.selectBorrowingRecordsWithDetails(
            page,
            queryDTO.getUserId(),
            queryDTO.getBookId(),
            queryDTO.getStatus(),
            queryDTO.getOverdue()
        );
        
        PageResult<BorrowingRecordVO> result = new PageResult<>();
        result.setCurrent(recordPage.getCurrent());
        result.setSize(recordPage.getSize());
        result.setTotal(recordPage.getTotal());
        result.setPages(recordPage.getPages());
        result.setRecords(recordPage.getRecords().stream()
            .map(this::convertToVO)
            .collect(Collectors.toList()));
        
        return result;
    }
    
    /**
     * 根据ID获取借阅记录详情
     * 
     * @param id 借阅记录ID
     * @return 借阅记录详情
     */
    public BorrowingRecordVO getBorrowingRecordById(Long id) {
        BorrowingRecord record = borrowingRecordMapper.selectById(id);
        if (record == null) {
            throw new BusinessException("借阅记录不存在");
        }
        
        return convertToVO(record);
    }
    
    /**
     * 获取用户当前借阅的图书
     * 
     * @param userId 用户ID
     * @return 借阅记录列表
     */
    public PageResult<BorrowingRecordVO> getUserCurrentBorrowings(Long userId, Long pageNum, Long pageSize) {
        BorrowingQueryDTO queryDTO = new BorrowingQueryDTO();
        queryDTO.setUserId(userId);
        queryDTO.setStatus("BORROWED");
        queryDTO.setPageNum(pageNum);
        queryDTO.setPageSize(pageSize);
        
        return getBorrowingRecords(queryDTO);
    }
    
    /**
     * 转换为VO对象
     * 
     * @param record 借阅记录
     * @return VO对象
     */
    private BorrowingRecordVO convertToVO(BorrowingRecord record) {
        BorrowingRecordVO vo = BeanUtil.copyProperties(record, BorrowingRecordVO.class);
        
        // 获取用户信息
        User user = userMapper.selectById(record.getUserId());
        if (user != null) {
            vo.setUsername(user.getUsername());
            vo.setUserRealName(user.getRealName());
        }
        
        // 获取图书信息
        Book book = bookMapper.selectById(record.getBookId());
        if (book != null) {
            vo.setBookTitle(book.getTitle());
            vo.setBookAuthor(book.getAuthor());
            vo.setBookIsbn(book.getIsbn());
        }
        
        // 计算是否逾期
        if (record.getStatus() == BorrowingRecord.BorrowStatus.BORROWED && 
            record.getDueTime().isBefore(LocalDateTime.now())) {
            vo.setOverdue(true);
            vo.setOverdueDays((int) ChronoUnit.DAYS.between(record.getDueTime(), LocalDateTime.now()));
        } else {
            vo.setOverdue(false);
            vo.setOverdueDays(0);
        }
        
        return vo;
    }
}
