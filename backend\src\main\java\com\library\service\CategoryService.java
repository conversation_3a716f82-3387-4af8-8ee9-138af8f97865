package com.library.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.library.common.BusinessException;
import com.library.dto.CategoryCreateDTO;
import com.library.entity.Category;
import com.library.mapper.BookMapper;
import com.library.mapper.CategoryMapper;
import com.library.vo.CategoryVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 分类服务类
 * 
 * <AUTHOR> Management System
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CategoryService {
    
    private final CategoryMapper categoryMapper;
    private final BookMapper bookMapper;
    
    /**
     * 获取分类树
     * 
     * @return 分类树
     */
    @Cacheable(value = "categories", key = "'tree'")
    public List<CategoryVO> getCategoryTree() {
        // 获取所有分类
        List<Category> allCategories = categoryMapper.selectList(
            new LambdaQueryWrapper<Category>()
                .orderByAsc(Category::getSortOrder)
                .orderByAsc(Category::getId)
        );
        
        // 转换为VO
        List<CategoryVO> categoryVOs = allCategories.stream()
            .map(category -> BeanUtil.copyProperties(category, CategoryVO.class))
            .collect(Collectors.toList());
        
        // 构建树形结构
        return buildCategoryTree(categoryVOs, 0L);
    }
    
    /**
     * 获取所有顶级分类
     * 
     * @return 顶级分类列表
     */
    @Cacheable(value = "categories", key = "'top'")
    public List<CategoryVO> getTopCategories() {
        List<Category> categories = categoryMapper.findTopCategories();
        return categories.stream()
            .map(category -> BeanUtil.copyProperties(category, CategoryVO.class))
            .collect(Collectors.toList());
    }
    
    /**
     * 根据父分类ID获取子分类
     * 
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    @Cacheable(value = "categories", key = "'children:' + #parentId")
    public List<CategoryVO> getCategoriesByParentId(Long parentId) {
        List<Category> categories = categoryMapper.findByParentId(parentId);
        return categories.stream()
            .map(category -> BeanUtil.copyProperties(category, CategoryVO.class))
            .collect(Collectors.toList());
    }
    
    /**
     * 根据ID获取分类详情
     * 
     * @param id 分类ID
     * @return 分类详情
     */
    @Cacheable(value = "category", key = "#id")
    public CategoryVO getCategoryById(Long id) {
        Category category = categoryMapper.selectById(id);
        if (category == null) {
            throw new BusinessException("分类不存在");
        }
        return BeanUtil.copyProperties(category, CategoryVO.class);
    }
    
    /**
     * 添加分类
     * 
     * @param createDTO 创建DTO
     * @return 分类详情
     */
    @Transactional
    @CacheEvict(value = {"categories", "category"}, allEntries = true)
    public CategoryVO addCategory(CategoryCreateDTO createDTO) {
        // 检查分类名称是否已存在（同一父分类下）
        LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Category::getName, createDTO.getName())
               .eq(Category::getParentId, createDTO.getParentId());
        
        if (categoryMapper.selectOne(wrapper) != null) {
            throw new BusinessException("同一父分类下已存在相同名称的分类");
        }
        
        // 如果有父分类，检查父分类是否存在
        if (createDTO.getParentId() != null && createDTO.getParentId() > 0) {
            if (categoryMapper.selectById(createDTO.getParentId()) == null) {
                throw new BusinessException("父分类不存在");
            }
        }
        
        Category category = BeanUtil.copyProperties(createDTO, Category.class);
        categoryMapper.insert(category);
        
        log.info("Category added: {}", category.getName());
        return getCategoryById(category.getId());
    }
    
    /**
     * 更新分类
     * 
     * @param id 分类ID
     * @param updateDTO 更新DTO
     * @return 分类详情
     */
    @Transactional
    @CacheEvict(value = {"categories", "category"}, allEntries = true)
    public CategoryVO updateCategory(Long id, CategoryCreateDTO updateDTO) {
        Category existingCategory = categoryMapper.selectById(id);
        if (existingCategory == null) {
            throw new BusinessException("分类不存在");
        }
        
        // 检查分类名称是否已存在（同一父分类下，排除自己）
        LambdaQueryWrapper<Category> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Category::getName, updateDTO.getName())
               .eq(Category::getParentId, updateDTO.getParentId())
               .ne(Category::getId, id);
        
        if (categoryMapper.selectOne(wrapper) != null) {
            throw new BusinessException("同一父分类下已存在相同名称的分类");
        }
        
        // 如果有父分类，检查父分类是否存在，且不能设置自己为父分类
        if (updateDTO.getParentId() != null && updateDTO.getParentId() > 0) {
            if (updateDTO.getParentId().equals(id)) {
                throw new BusinessException("不能设置自己为父分类");
            }
            if (categoryMapper.selectById(updateDTO.getParentId()) == null) {
                throw new BusinessException("父分类不存在");
            }
        }
        
        Category category = BeanUtil.copyProperties(updateDTO, Category.class);
        category.setId(id);
        categoryMapper.updateById(category);
        
        log.info("Category updated: {}", category.getName());
        return getCategoryById(id);
    }
    
    /**
     * 删除分类
     * 
     * @param id 分类ID
     */
    @Transactional
    @CacheEvict(value = {"categories", "category"}, allEntries = true)
    public void deleteCategory(Long id) {
        Category category = categoryMapper.selectById(id);
        if (category == null) {
            throw new BusinessException("分类不存在");
        }
        
        // 检查是否有子分类
        List<Category> children = categoryMapper.findByParentId(id);
        if (!children.isEmpty()) {
            throw new BusinessException("该分类下还有子分类，无法删除");
        }
        
        // 检查是否有图书使用该分类
        LambdaQueryWrapper<com.library.entity.Book> bookWrapper = new LambdaQueryWrapper<>();
        bookWrapper.eq(com.library.entity.Book::getCategoryId, id);
        if (bookMapper.selectCount(bookWrapper) > 0) {
            throw new BusinessException("该分类下还有图书，无法删除");
        }
        
        categoryMapper.deleteById(id);
        
        log.info("Category deleted: {}", category.getName());
    }
    
    /**
     * 构建分类树
     * 
     * @param categories 所有分类
     * @param parentId 父分类ID
     * @return 分类树
     */
    private List<CategoryVO> buildCategoryTree(List<CategoryVO> categories, Long parentId) {
        Map<Long, List<CategoryVO>> categoryMap = categories.stream()
            .collect(Collectors.groupingBy(CategoryVO::getParentId));
        
        List<CategoryVO> result = categoryMap.get(parentId);
        if (result == null) {
            result = new ArrayList<>();
        }
        return result.stream()
            .peek(category -> category.setChildren(buildCategoryTree(categories, category.getId())))
            .collect(Collectors.toList());
    }
}
