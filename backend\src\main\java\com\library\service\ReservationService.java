package com.library.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.library.common.BusinessException;
import com.library.common.PageResult;
import com.library.dto.ReservationRequest;
import com.library.entity.Book;
import com.library.entity.Reservation;
import com.library.entity.User;
import com.library.mapper.BookMapper;
import com.library.mapper.ReservationMapper;
import com.library.mapper.UserMapper;
import com.library.vo.ReservationVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.stream.Collectors;

/**
 * 预约管理服务类
 * 
 * <AUTHOR> Management System
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ReservationService {
    
    private final ReservationMapper reservationMapper;
    private final BookMapper bookMapper;
    private final UserMapper userMapper;
    
    /**
     * 创建预约
     * 
     * @param userId 用户ID
     * @param reservationRequest 预约请求
     * @return 预约记录
     */
    @Transactional
    public ReservationVO createReservation(Long userId, ReservationRequest reservationRequest) {
        // 检查用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 检查图书是否存在
        Book book = bookMapper.selectById(reservationRequest.getBookId());
        if (book == null) {
            throw new BusinessException("图书不存在");
        }
        
        // 检查图书是否有库存（如果有库存，不需要预约）
        if (book.getAvailableCopies() > 0) {
            throw new BusinessException("图书有库存，可直接借阅，无需预约");
        }
        
        // 检查用户是否已预约该图书
        Reservation existingReservation = reservationMapper.findActiveByUserAndBook(userId, reservationRequest.getBookId());
        if (existingReservation != null) {
            throw new BusinessException("您已预约该图书，请勿重复预约");
        }
        
        // 检查用户当前预约数量是否超限（假设最多预约3本）
        int currentReservationCount = reservationMapper.countActiveByUser(userId);
        if (currentReservationCount >= 3) {
            throw new BusinessException("您当前预约的图书数量已达上限（3本），请先取消部分预约");
        }
        
        // 创建预约记录
        Reservation reservation = new Reservation();
        reservation.setUserId(userId);
        reservation.setBookId(reservationRequest.getBookId());
        reservation.setReservationTime(LocalDateTime.now());
        reservation.setExpirationTime(LocalDateTime.now().plusDays(7)); // 预约7天有效
        reservation.setStatus(Reservation.ReservationStatus.ACTIVE);
        reservation.setRemarks(reservationRequest.getRemarks());
        
        reservationMapper.insert(reservation);
        
        log.info("User {} created reservation for book {}", userId, reservationRequest.getBookId());
        
        return getReservationById(reservation.getId());
    }
    
    /**
     * 取消预约
     * 
     * @param reservationId 预约ID
     * @return 预约记录
     */
    @Transactional
    public ReservationVO cancelReservation(Long reservationId) {
        Reservation reservation = reservationMapper.selectById(reservationId);
        if (reservation == null) {
            throw new BusinessException("预约记录不存在");
        }
        
        if (reservation.getStatus() != Reservation.ReservationStatus.ACTIVE) {
            throw new BusinessException("预约已取消或已失效，无法重复操作");
        }
        
        // 更新预约状态
        reservation.setStatus(Reservation.ReservationStatus.CANCELLED);
        reservationMapper.updateById(reservation);
        
        log.info("Reservation {} cancelled", reservationId);
        
        return getReservationById(reservationId);
    }
    
    /**
     * 分页查询预约记录
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param userId 用户ID（可选）
     * @param bookId 图书ID（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    public PageResult<ReservationVO> getReservations(Long pageNum, Long pageSize, Long userId, Long bookId, String status) {
        Page<Reservation> page = new Page<>(pageNum, pageSize);
        
        Page<Reservation> reservationPage = (Page<Reservation>) reservationMapper.selectReservationsWithDetails(
            page, userId, bookId, status
        );
        
        PageResult<ReservationVO> result = new PageResult<>();
        result.setCurrent(reservationPage.getCurrent());
        result.setSize(reservationPage.getSize());
        result.setTotal(reservationPage.getTotal());
        result.setPages(reservationPage.getPages());
        result.setRecords(reservationPage.getRecords().stream()
            .map(this::convertToVO)
            .collect(Collectors.toList()));
        
        return result;
    }
    
    /**
     * 根据ID获取预约详情
     * 
     * @param id 预约ID
     * @return 预约详情
     */
    public ReservationVO getReservationById(Long id) {
        Reservation reservation = reservationMapper.selectById(id);
        if (reservation == null) {
            throw new BusinessException("预约记录不存在");
        }
        
        return convertToVO(reservation);
    }
    
    /**
     * 获取用户当前的预约
     * 
     * @param userId 用户ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 预约记录列表
     */
    public PageResult<ReservationVO> getUserActiveReservations(Long userId, Long pageNum, Long pageSize) {
        return getReservations(pageNum, pageSize, userId, null, "ACTIVE");
    }
    
    /**
     * 处理过期预约（定时任务调用）
     */
    @Transactional
    public void processExpiredReservations() {
        reservationMapper.updateExpiredReservations();
        log.info("Processed expired reservations");
    }
    
    /**
     * 转换为VO对象
     * 
     * @param reservation 预约记录
     * @return VO对象
     */
    private ReservationVO convertToVO(Reservation reservation) {
        ReservationVO vo = BeanUtil.copyProperties(reservation, ReservationVO.class);
        
        // 获取用户信息
        User user = userMapper.selectById(reservation.getUserId());
        if (user != null) {
            vo.setUsername(user.getUsername());
            vo.setUserRealName(user.getRealName());
        }
        
        // 获取图书信息
        Book book = bookMapper.selectById(reservation.getBookId());
        if (book != null) {
            vo.setBookTitle(book.getTitle());
            vo.setBookAuthor(book.getAuthor());
            vo.setBookIsbn(book.getIsbn());
        }
        
        return vo;
    }
}
