package com.library.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 图书视图对象
 * 
 * <AUTHOR> Management System
 */
@Data
public class BookVO {
    
    /**
     * 图书ID
     */
    private Long id;
    
    /**
     * ISBN号
     */
    private String isbn;
    
    /**
     * 书名
     */
    private String title;
    
    /**
     * 作者
     */
    private String author;
    
    /**
     * 出版社
     */
    private String publisher;
    
    /**
     * 出版日期
     */
    private LocalDate publishDate;
    
    /**
     * 分类ID
     */
    private Long categoryId;
    
    /**
     * 分类名称
     */
    private String categoryName;
    
    /**
     * 总册数
     */
    private Integer totalCopies;
    
    /**
     * 可借册数
     */
    private Integer availableCopies;
    
    /**
     * 价格
     */
    private BigDecimal price;
    
    /**
     * 图书描述
     */
    private String description;
    
    /**
     * 封面图片URL
     */
    private String coverImage;
    
    /**
     * 存放位置
     */
    private String location;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
