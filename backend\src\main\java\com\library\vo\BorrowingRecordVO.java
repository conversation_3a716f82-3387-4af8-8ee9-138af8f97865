package com.library.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 借阅记录视图对象
 * 
 * <AUTHOR> Management System
 */
@Data
public class BorrowingRecordVO {
    
    /**
     * 借阅记录ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户真实姓名
     */
    private String userRealName;
    
    /**
     * 图书ID
     */
    private Long bookId;
    
    /**
     * 图书标题
     */
    private String bookTitle;
    
    /**
     * 图书作者
     */
    private String bookAuthor;
    
    /**
     * 图书ISBN
     */
    private String bookIsbn;
    
    /**
     * 借阅时间
     */
    private LocalDateTime borrowTime;
    
    /**
     * 应还时间
     */
    private LocalDateTime dueTime;
    
    /**
     * 实际归还时间
     */
    private LocalDateTime returnTime;
    
    /**
     * 续借次数
     */
    private Integer renewalCount;
    
    /**
     * 借阅状态
     */
    private String status;
    
    /**
     * 是否逾期
     */
    private Boolean overdue;
    
    /**
     * 逾期天数
     */
    private Integer overdueDays;
    
    /**
     * 备注
     */
    private String remarks;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
