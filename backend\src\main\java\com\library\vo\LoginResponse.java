package com.library.vo;

import lombok.Data;

/**
 * 登录响应VO
 * 
 * <AUTHOR> Management System
 */
@Data
public class LoginResponse {
    
    /**
     * JWT Token
     */
    private String token;
    
    /**
     * Token类型
     */
    private String tokenType = "Bearer";
    
    /**
     * 用户信息
     */
    private UserInfo userInfo;
    
    public LoginResponse(String token, UserInfo userInfo) {
        this.token = token;
        this.userInfo = userInfo;
    }
    
    /**
     * 用户信息内部类
     */
    @Data
    public static class UserInfo {
        private Long id;
        private String username;
        private String email;
        private String realName;
        private String avatar;
        private String role;
        private String status;
    }
}
