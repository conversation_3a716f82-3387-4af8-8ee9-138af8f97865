package com.library.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 预约视图对象
 * 
 * <AUTHOR> Management System
 */
@Data
public class ReservationVO {
    
    /**
     * 预约ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户真实姓名
     */
    private String userRealName;
    
    /**
     * 图书ID
     */
    private Long bookId;
    
    /**
     * 图书标题
     */
    private String bookTitle;
    
    /**
     * 图书作者
     */
    private String bookAuthor;
    
    /**
     * 图书ISBN
     */
    private String bookIsbn;
    
    /**
     * 预约时间
     */
    private LocalDateTime reservationTime;
    
    /**
     * 过期时间
     */
    private LocalDateTime expirationTime;
    
    /**
     * 预约状态
     */
    private String status;
    
    /**
     * 备注
     */
    private String remarks;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
