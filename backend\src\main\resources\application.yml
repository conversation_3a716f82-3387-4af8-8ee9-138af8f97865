server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: library-management-system
  
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: LibraryHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/*.xml

# JWT配置
jwt:
  secret: library-management-system-jwt-secret-key-2024
  expiration: 86400000 # 24小时
  header: Authorization
  prefix: Bearer 

# 日志配置
logging:
  level:
    com.library: debug
    org.springframework.security: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'

# Swagger配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: 图书管理系统API
    description: 图书管理系统后端接口文档
    version: 1.0.0
    contact:
      name: Library Management System
      email: <EMAIL>

# 应用自定义配置
app:
  # 文件上传配置
  upload:
    path: ./uploads/
    max-file-size: 10MB
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx
  
  # 借阅规则配置
  borrow:
    max-books-per-user: 5
    default-borrow-days: 30
    max-renew-times: 2
    fine-per-day: 0.5
